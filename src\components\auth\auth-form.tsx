"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Form, FormField, FormMessage } from '@/components/ui/form'
import { PasswordInput } from './password-input'
import { showErrorToast } from '@/lib/toast'
import { cn } from '@/lib/utils'
import { AlertCircle, CheckCircle2 } from '@/components/ui/icons'

interface FormFieldConfig {
  name: string
  label: string
  type: 'text' | 'email' | 'password'
  placeholder?: string
  required?: boolean
  showStrengthIndicator?: boolean
}

interface AuthFormProps {
  fields: FormFieldConfig[]
  submitLabel: string
  onSubmit: (data: Record<string, string>) => Promise<void>
  isLoading?: boolean
  className?: string
}

export function AuthForm({
  fields,
  submitLabel,
  onSubmit,
  isLoading = false,
  className
}: AuthFormProps) {
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isValidating, setIsValidating] = useState(false)

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear error when user starts typing and validate in real-time
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }

    // Real-time validation for better UX
    if (touched[name]) {
      const field = fields.find(f => f.name === name)
      if (field) {
        const error = validateField(field, value)
        setErrors(prev => ({ ...prev, [name]: error }))
      }
    }
  }

  const handleInputBlur = (name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }))

    // Validate on blur
    const field = fields.find(f => f.name === name)
    if (field) {
      const value = formData[name] || ''
      const error = validateField(field, value)
      setErrors(prev => ({ ...prev, [name]: error }))
    }
  }

  const validateField = (field: FormFieldConfig, value: string): string => {
    if (field.required && !value.trim()) {
      return `${field.label} is required`
    }

    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address'
      }
    }

    if (field.type === 'password' && value && field.name === 'password') {
      if (value.length < 8) {
        return 'Password must be at least 8 characters long'
      }
      if (!/[a-z]/.test(value)) {
        return 'Password must contain at least one lowercase letter'
      }
      if (!/[A-Z]/.test(value)) {
        return 'Password must contain at least one uppercase letter'
      }
      if (!/[0-9]/.test(value)) {
        return 'Password must contain at least one number'
      }
    }

    if (field.name === 'confirmPassword' && value) {
      const password = formData.password || ''
      if (password !== value) {
        return 'Passwords do not match'
      }
    }

    if (field.name === 'name' && value) {
      if (value.length < 2) {
        return 'Name must be at least 2 characters long'
      }
      if (value.length > 50) {
        return 'Name must be less than 50 characters'
      }
    }

    return ''
  }

  const validateForm = (): boolean => {
    setIsValidating(true)
    const newErrors: Record<string, string> = {}
    const newTouched: Record<string, boolean> = {}

    fields.forEach(field => {
      const value = formData[field.name] || ''
      const error = validateField(field, value)
      newTouched[field.name] = true
      if (error) {
        newErrors[field.name] = error
      }
    })

    setErrors(newErrors)
    setTouched(newTouched)
    setIsValidating(false)

    const hasErrors = Object.keys(newErrors).length > 0

    if (hasErrors) {
      // Show validation error toast
      const errorCount = Object.keys(newErrors).length
      const firstError = Object.values(newErrors)[0]

      showErrorToast(
        "Validation Error",
        errorCount === 1 ? firstError : `Please fix ${errorCount} errors in the form`
      )
    }

    return !hasErrors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
      // Success toast will be handled by the parent component
    } catch (error) {
      // Don't show toast here - let parent component handle it
      // This prevents duplicate error messages
    }
  }

  return (
    <Form onSubmit={handleSubmit} className={cn("space-y-6", className)}>
      {fields.map((field) => {
        const value = formData[field.name] || ''
        const error = errors[field.name]
        const isTouched = touched[field.name]
        const showError = error && isTouched
        const isValid = isTouched && !error && value.trim() !== ''

        return (
          <FormField key={field.name} className="space-y-2">
            <Label htmlFor={field.name} className={cn(
              "text-sm font-medium transition-colors",
              showError ? "text-red-600 dark:text-red-400" : "text-foreground"
            )}>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>

            <div className="relative">
              {field.type === 'password' ? (
                <PasswordInput
                  id={field.name}
                  name={field.name}
                  placeholder={field.placeholder}
                  value={value}
                  onChange={(e) => handleInputChange(field.name, e.target.value)}
                  onBlur={() => handleInputBlur(field.name)}
                  showStrengthIndicator={field.showStrengthIndicator}
                  className={cn(
                    "transition-all duration-300 pr-10",
                    showError && "border-red-500 bg-red-50 dark:bg-red-900/10 focus-visible:ring-red-500 shadow-sm shadow-red-500/20",
                    isValid && "border-green-500 bg-green-50 dark:bg-green-900/10 focus-visible:ring-green-500 shadow-sm shadow-green-500/20"
                  )}
                  required={field.required}
                />
              ) : (
                <Input
                  id={field.name}
                  name={field.name}
                  type={field.type}
                  placeholder={field.placeholder}
                  value={value}
                  onChange={(e) => handleInputChange(field.name, e.target.value)}
                  onBlur={() => handleInputBlur(field.name)}
                  className={cn(
                    "transition-all duration-300 pr-10",
                    showError && "border-red-500 bg-red-50 dark:bg-red-900/10 focus-visible:ring-red-500 shadow-sm shadow-red-500/20",
                    isValid && "border-green-500 bg-green-50 dark:bg-green-900/10 focus-visible:ring-green-500 shadow-sm shadow-green-500/20"
                  )}
                  required={field.required}
                />
              )}

              {/* Validation Icons */}
              {isTouched && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {showError ? (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  ) : isValid ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  ) : null}
                </div>
              )}
            </div>

            {showError && (
              <FormMessage variant="error" className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                {error}
              </FormMessage>
            )}
          </FormField>
        )
      })}

      <Button
        type="submit"
        className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 transition-all duration-300 hover:scale-105"
        disabled={isLoading}
      >
        {isLoading ? "Please wait..." : submitLabel}
      </Button>
    </Form>
  )
}
