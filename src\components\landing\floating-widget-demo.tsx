"use client"

import { useState, useEffect } from 'react'
import { MessageSquare, Users, BarChart3, Send, Minimize2, X } from '@/components/ui/icons'

const messages = [
  { id: 1, text: "Hi there! 👋 Welcome to our website! How can I help you today?", sender: "bot", time: "2:30 PM", avatar: "S" },
  { id: 2, text: "Hi! I have a question about your services", sender: "user", time: "2:31 PM", avatar: "You" },
  { id: 3, text: "I'd be happy to help! What would you like to know?", sender: "bot", time: "2:31 PM", avatar: "S" },
  { id: 4, text: "How long does delivery usually take?", sender: "user", time: "2:32 PM", avatar: "You" },
  { id: 5, text: "Great question! Standard delivery typically takes 3-5 business days. We also offer express shipping for next-day delivery. Would you like more details about our shipping options?", sender: "bot", time: "2:32 PM", avatar: "S" },
]

export function FloatingWidgetDemo() {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [currentMessage, setCurrentMessage] = useState(0)
  const [displayedMessages, setDisplayedMessages] = useState<typeof messages>([])
  const [isTyping, setIsTyping] = useState(false)
  const [inputValue, setInputValue] = useState("")
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (isOpen && !isMinimized && currentMessage < messages.length) {
      const timer = setTimeout(() => {
        if (messages[currentMessage].sender === 'bot') {
          setIsTyping(true)
          setTimeout(() => {
            setDisplayedMessages(prev => [...prev, messages[currentMessage]])
            setCurrentMessage(prev => prev + 1)
            setIsTyping(false)
          }, 1500)
        } else {
          setDisplayedMessages(prev => [...prev, messages[currentMessage]])
          setCurrentMessage(prev => prev + 1)
        }
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [isOpen, isMinimized, currentMessage])

  const resetDemo = () => {
    setDisplayedMessages([])
    setCurrentMessage(0)
    setIsTyping(false)
    setInputValue("")
    setIsOpen(false)
    setIsMinimized(false)
    setTimeout(() => setIsOpen(true), 500)
  }

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      // Use static time during SSR, dynamic time after mounting
      const currentTime = mounted ? new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : "2:33 PM"
      const messageId = mounted ? Date.now() : Math.floor(Math.random() * 1000000)

      setDisplayedMessages(prev => [...prev, {
        id: messageId,
        text: inputValue,
        sender: "user",
        time: currentTime,
        avatar: "You"
      }])
      setInputValue("")

      // Auto-reply after user message
      setTimeout(() => {
        setIsTyping(true)
        setTimeout(() => {
          const replyTime = mounted ? new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : "2:33 PM"
          const replyId = mounted ? Date.now() + 1 : Math.floor(Math.random() * 1000000) + 1

          setDisplayedMessages(prev => [...prev, {
            id: replyId,
            text: "Thanks for your message! I'll connect you with the right team member who can help you with that. Please hold on for just a moment.",
            sender: "bot",
            time: replyTime,
            avatar: "S"
          }])
          setIsTyping(false)
        }, 1500)
      }, 1000)
    }
  }

  return (
    <div className="fixed bottom-6 right-6 z-50 font-sans">
      {/* Floating Stats */}
      <div className="absolute -top-32 -left-20 space-y-4 pointer-events-none">
  
      </div>

      {/* Chat Widget */}
      {isOpen ? (
        <div className="w-80 h-96 bg-background border border-border rounded-2xl shadow-2xl flex flex-col overflow-hidden backdrop-blur-xl">
          {/* Header */}
          <div className="bg-gradient-to-r from-pink-500 to-purple-600 p-4 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <MessageSquare className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-white">Support Chat</h4>
                <p className="text-xs text-white/80">We're here to help!</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsMinimized(!isMinimized)}
                className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
              >
                <Minimize2 className="w-3 h-3 text-white" />
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
              >
                <X className="w-3 h-3 text-white" />
              </button>
            </div>
          </div>

          {!isMinimized && (
            <div className="flex-1 p-6 space-y-4 overflow-y-auto bg-gradient-to-b from-background/50 to-muted/30 custom-scrollbar">
              {/* Welcome Message */}
              {displayedMessages.length === 0 && (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                    <MessageSquare className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="font-semibold text-foreground mb-2">Welcome!</h4>
                  <p className="text-sm text-muted-foreground">How can we help you today?</p>
                </div>
              )}

              {displayedMessages.map((message, index) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} animate-slide-in-from-bottom`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className={`max-w-xs px-4 py-2 rounded-2xl ${message.sender === 'user'
                      ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white'
                      : 'bg-muted text-foreground border border-border'
                    }`}>
                    <p className="text-sm">{message.text}</p>
                    <p className={`text-xs mt-1 ${message.sender === 'user' ? 'text-white/70' : 'text-muted-foreground'
                      }`}>
                      {message.time}
                    </p>
                  </div>
                </div>
              ))}

              {isTyping && (
                <div className="flex justify-start animate-bounce-in">
                  <div className="bg-muted text-foreground border border-border px-4 py-2 rounded-2xl">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {!isMinimized && (
            <div className="p-4 border-t border-border bg-background/80 backdrop-blur-sm">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type your message..."
                  className="flex-1 px-3 py-2 bg-muted border border-border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <button
                  onClick={handleSendMessage}
                  className="px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
              <div className="flex items-center justify-between mt-3">
                <p className="text-xs text-muted-foreground">Powered by ChattyFlow</p>
                <button
                  onClick={resetDemo}
                  className="text-xs text-primary hover:text-primary/80 transition-colors"
                >
                  Reset Demo
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <button
          onClick={() => setIsOpen(true)}
          className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full shadow-2xl flex items-center justify-center hover:scale-110 transition-all duration-300 animate-pulse"
        >
          <MessageSquare className="w-8 h-8 text-white" />
        </button>
      )}
    </div>
  )
}
