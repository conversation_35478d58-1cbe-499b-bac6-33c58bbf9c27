@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/* CSS Variables for theming */
:root {
  --background: 249 250 251;
  /* gray-50 */
  --foreground: 17 24 39;
  /* gray-900 */
  --card: 255 255 255;
  /* white */
  --card-foreground: 17 24 39;
  /* gray-900 */
  --popover: 255 255 255;
  /* white */
  --popover-foreground: 17 24 39;
  /* gray-900 */
  --primary: 97 92 237;
  /* #615ced */
  --primary-foreground: 255 255 255;
  /* white */
  --secondary: 243 244 246;
  /* gray-100 */
  --secondary-foreground: 17 24 39;
  /* gray-900 */
  --muted: 243 244 246;
  /* gray-100 */
  --muted-foreground: 107 114 128;
  /* gray-500 */
  --accent: 243 244 246;
  /* gray-100 */
  --accent-foreground: 17 24 39;
  /* gray-900 */
  --destructive: 239 68 68;
  /* red-500 */
  --destructive-foreground: 255 255 255;
  /* white */
  --border: 229 231 235;
  /* gray-200 */
  --input: 229 231 235;
  /* gray-200 */
  --ring: 97 92 237;
  /* #615ced */
  --radius: 0.5rem;
}

.dark {
  --background: 17 24 39;
  /* gray-900 */
  --foreground: 249 250 251;
  /* gray-50 */
  --card: 31 41 55;
  /* gray-800 */
  --card-foreground: 249 250 251;
  /* gray-50 */
  --popover: 31 41 55;
  /* gray-800 */
  --popover-foreground: 249 250 251;
  /* gray-50 */
  --primary: 97 92 237;
  /* #615ced */
  --primary-foreground: 255 255 255;
  /* white */
  --secondary: 55 65 81;
  /* gray-700 */
  --secondary-foreground: 249 250 251;
  /* gray-50 */
  --muted: 55 65 81;
  /* gray-700 */
  --muted-foreground: 156 163 175;
  /* gray-400 */
  --accent: 55 65 81;
  /* gray-700 */
  --accent-foreground: 249 250 251;
  /* gray-50 */
  --destructive: 239 68 68;
  /* red-500 */
  --destructive-foreground: 255 255 255;
  /* white */
  --border: 55 65 81;
  /* gray-700 */
  --input: 55 65 81;
  /* gray-700 */
  --ring: 97 92 237;
  /* #615ced */
}

* {
  border-color: rgb(var(--border));
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
  transition: background-color 0.5s cubic-bezier(0.4, 0, 0.2, 1), color 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Glassmorphism styles */
.glass {
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass:hover {
  background: rgba(255, 255, 255, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.35);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.dark .glass:hover {
  background: rgba(0, 0, 0, 0.35);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(var(--muted-foreground));
  border-radius: 9999px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--foreground) / 0.7);
}

/* Enhanced smooth transitions for theme changes */
*,
*::before,
*::after {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Theme toggle specific animations */
[data-theme-toggle] {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced button hover effects */
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Custom animations */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes scale-x {
  0% {
    transform: scaleX(0);
  }

  100% {
    transform: scaleX(1);
  }
}

@keyframes slide-in-from-bottom {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes gradient-shift {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

@keyframes pulse-glow {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(14, 105, 78, 0.3);
  }

  50% {
    box-shadow: 0 0 30px rgba(14, 105, 78, 0.6);
  }
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes tab-switch {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes progress-fill {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}

@keyframes feature-highlight {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(14, 105, 78, 0.2);
  }

  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(14, 105, 78, 0.4);
  }
}

@keyframes demo-element-appear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-slide-in-from-bottom {
  animation: slide-in-from-bottom 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-in-left {
  animation: slide-in-left 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.8s ease-out;
}

.animate-tab-switch {
  animation: tab-switch 0.5s ease-out;
}

.animate-feature-highlight {
  animation: feature-highlight 2s ease-in-out infinite;
}

.animate-demo-element-appear {
  animation: demo-element-appear 0.6s ease-out;
}

/* Staggered animations */
.animate-stagger-1 {
  animation-delay: 0.1s;
}

.animate-stagger-2 {
  animation-delay: 0.2s;
}

.animate-stagger-3 {
  animation-delay: 0.3s;
}

.animate-stagger-4 {
  animation-delay: 0.4s;
}

.animate-stagger-5 {
  animation-delay: 0.5s;
}

.animate-stagger-6 {
  animation-delay: 0.6s;
}

/* Interactive demo specific styles */
.demo-tab-active {
  background: linear-gradient(135deg, rgba(14, 105, 78, 0.1), rgba(168, 85, 247, 0.1));
  border: 1px solid rgba(14, 105, 78, 0.3);
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(14, 105, 78, 0.2);
}

.demo-preview-content {
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.demo-progress-bar {
  background: linear-gradient(90deg, transparent, rgba(14, 105, 78, 0.3), transparent);
  animation: progress-fill 5s linear infinite;
}

/* Cursor states for interactive elements */
button,
a,
[role="button"],
.cursor-pointer,
input[type="button"],
input[type="submit"],
input[type="reset"] {
  cursor: pointer;
}

button:disabled,
input:disabled,
.cursor-not-allowed {
  cursor: not-allowed;
}

/* Stable positioning for fixed elements */
.fixed {
  will-change: auto;
}

/* Prevent layout shifts during animations */
.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Ensure interactive elements are properly layered */
.interactive-layer {
  position: relative;
  z-index: 10;
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible,
[role="button"]:focus-visible {
  outline: 2px solid rgb(var(--primary));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Improved backdrop blur for navigation */
nav {
  backdrop-filter: blur(20px) saturate(180%);
}

/* Enhanced gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #0e694e, #a855f7);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

/* Improved card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .card-hover:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Scale utility classes */
.hover\:scale-102:hover {
  transform: scale(1.02);
}