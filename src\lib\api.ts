interface ApiResponse<T = any> {
    success: boolean
    message: string
    user?: T
    errors?: Record<string, string[]>
    error?: string
}

interface User {
    id: number
    name: string
    email: string
    initials: string
    created_at: string
}

interface RegisterData {
    name: string
    email: string
    password: string
    password_confirmation: string
}

interface LoginData {
    email: string
    password: string
}

class ApiService {
    private baseURL: string
    private apiURL: string

    constructor() {
        this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
        this.apiURL = `${this.baseURL}/api`
    }

    /**
     * Get CSRF token from Laravel Sanctum
     */
    private async getCsrfToken(): Promise<void> {
        await fetch(`${this.baseURL}/sanctum/csrf-cookie`, {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Accept': 'application/json',
            },
        })
    }

    /**
     * Make authenticated request with CSRF protection
     */
    private async request<T = any>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        const url = `${this.apiURL}${endpoint}`

        const defaultHeaders: Record<string, string> = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        }

        const config: RequestInit = {
            ...options,
            credentials: 'include', // Include cookies for session auth
            headers: {
                ...defaultHeaders,
                ...options.headers,
            },
        }

        try {
            const response = await fetch(url, config)
            const data = await response.json()

            if (!response.ok) {
                // Handle authentication errors
                if (response.status === 401) {
                    this.clearAuth()
                    if (typeof window !== 'undefined') {
                        window.location.href = '/auth/login'
                    }
                    throw new Error('Session expired. Please login again.')
                }

                // Handle validation errors
                if (response.status === 422 && data.errors) {
                    throw {
                        message: data.message || 'Validation failed',
                        errors: data.errors,
                        status: response.status
                    }
                }

                throw new Error(data.message || `HTTP error! status: ${response.status}`)
            }

            return data
        } catch (error) {
            if (error instanceof Error) {
                throw error
            }
            // Handle network errors
            throw new Error('Network error occurred. Please check your connection.')
        }
    }

    // Authentication methods
    async register(data: RegisterData): Promise<ApiResponse<User>> {
        // Get CSRF token first
        await this.getCsrfToken()

        const response = await this.request<User>('/auth/register', {
            method: 'POST',
            body: JSON.stringify(data),
        })

        if (response.success && response.user) {
            this.setStoredUser(response.user)
        }

        return response
    }

    async login(data: LoginData): Promise<ApiResponse<User>> {
        // Get CSRF token first
        await this.getCsrfToken()

        const response = await this.request<User>('/auth/login', {
            method: 'POST',
            body: JSON.stringify(data),
        })

        if (response.success && response.user) {
            this.setStoredUser(response.user)
        }

        return response
    }

    async logout(): Promise<ApiResponse> {
        try {
            const response = await this.request('/auth/logout', {
                method: 'POST',
            })

            this.clearAuth()
            return response
        } catch (error) {
            // Clear auth even if logout request fails
            this.clearAuth()
            throw error
        }
    }

    async getUser(): Promise<ApiResponse<User>> {
        const response = await this.request<User>('/auth/user')

        if (response.success && response.user) {
            this.setStoredUser(response.user)
        }

        return response
    }

    // Utility methods
    private setStoredUser(user: User | null): void {
        if (typeof window === 'undefined') return

        if (user) {
            localStorage.setItem('auth_user', JSON.stringify(user))
        } else {
            localStorage.removeItem('auth_user')
        }
    }

    private getStoredUser(): User | null {
        if (typeof window === 'undefined') return null
        const user = localStorage.getItem('auth_user')
        return user ? JSON.parse(user) : null
    }

    clearAuth(): void {
        if (typeof window === 'undefined') return
        localStorage.removeItem('auth_user')
    }

    isAuthenticated(): boolean {
        return !!this.getStoredUser()
    }

    getCurrentUser(): User | null {
        return this.getStoredUser()
    }

    // Health check
    async healthCheck(): Promise<ApiResponse> {
        return this.request('/health')
    }
}

// Export singleton instance
export const apiService = new ApiService()

// Export types
export type { ApiResponse, User, RegisterData, LoginData } 