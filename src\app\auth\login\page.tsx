"use client"

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { AuthLayout } from '@/components/auth/auth-layout'
import { AuthForm } from '@/components/auth/auth-form'
import { Button } from '@/components/ui/button'
import { showSuccessToast, showErrorToast } from '@/lib/toast'
import { apiService, type LoginData } from '@/lib/api'

const loginFields = [
  {
    name: 'email',
    label: 'Email Address',
    type: 'email' as const,
    placeholder: 'Enter your email address',
    required: true
  },
  {
    name: 'password',
    label: 'Password',
    type: 'password' as const,
    placeholder: 'Enter your password',
    required: true
  }
]

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (data: Record<string, string>) => {
    setIsLoading(true)

    try {
      const loginData: LoginData = {
        email: data.email,
        password: data.password
      }

      const response = await apiService.login(loginData)

      if (response.success && response.user) {
        showSuccessToast(
          "🎉 Welcome back!",
          `Successfully signed in as ${response.user.name}. Redirecting to your dashboard...`
        )

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard')
        }, 1500)
      } else {
        throw new Error(response.message || 'Login failed')
      }
    } catch (error: any) {
      let errorMessage = 'Something went wrong. Please try again.'

      // Handle validation errors
      if (error.errors) {
        const firstError = Object.values(error.errors as Record<string, string[]>)[0]?.[0]
        errorMessage = firstError || error.message
      } else if (error instanceof Error) {
        errorMessage = error.message
      }

      showErrorToast(
        "❌ Sign In Failed",
        errorMessage
      )
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout
      title="Welcome Back"
      subtitle="Sign in to your ChattyFlow account"
    >
      <div className="space-y-6">
        <AuthForm
          fields={loginFields}
          submitLabel="Sign In"
          onSubmit={handleLogin}
          isLoading={isLoading}
        />

        {/* Additional Links */}
        <div className="space-y-4">
          <div className="text-center">
            <Link
              href="/auth/forgot-password"
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              Forgot your password?
            </Link>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-border" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Don't have an account?
              </span>
            </div>
          </div>

          <Link href="/auth/register">
            <Button
              variant="outline"
              className="w-full hover:scale-105 transition-transform"
            >
              Create Account
            </Button>
          </Link>
        </div>
      </div>
    </AuthLayout>
  )
}
