"use client"

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { AuthLayout } from '@/components/auth/auth-layout'
import { AuthForm } from '@/components/auth/auth-form'
import { Button } from '@/components/ui/button'
import { showSuccessToast, showErrorToast } from '@/lib/toast'
import { apiService, type RegisterData } from '@/lib/api'

const registerFields = [
  {
    name: 'name',
    label: 'Full Name',
    type: 'text' as const,
    placeholder: 'Enter your full name',
    required: true
  },
  {
    name: 'email',
    label: 'Email Address',
    type: 'email' as const,
    placeholder: 'Enter your email address',
    required: true
  },
  {
    name: 'password',
    label: 'Password',
    type: 'password' as const,
    placeholder: 'Create a strong password',
    required: true,
    showStrengthIndicator: true
  },
  {
    name: 'confirmPassword',
    label: 'Confirm Password',
    type: 'password' as const,
    placeholder: 'Confirm your password',
    required: true
  }
]

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleRegister = async (data: Record<string, string>) => {
    setIsLoading(true)

    try {
      const registerData: RegisterData = {
        name: data.name,
        email: data.email,
        password: data.password,
        password_confirmation: data.confirmPassword
      }

      const response = await apiService.register(registerData)

      if (response.success && response.user) {
        showSuccessToast(
          "🎉 Welcome to ChattyFlow!",
          `Hi ${response.user.name}! Your account has been created successfully. Redirecting to your dashboard...`
        )

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard')
        }, 1500)
      } else {
        throw new Error(response.message || 'Registration failed')
      }
    } catch (error: any) {
      let errorMessage = 'Something went wrong. Please try again.'

      // Handle validation errors
      if (error.errors) {
        const firstError = Object.values(error.errors as Record<string, string[]>)[0]?.[0]
        errorMessage = firstError || error.message
      } else if (error instanceof Error) {
        errorMessage = error.message
      }

      showErrorToast(
        "❌ Registration Failed",
        errorMessage
      )
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthLayout
      title="Create Account"
      subtitle="Start building amazing chat widgets today"
    >
      <div className="space-y-6">
        <AuthForm
          fields={registerFields}
          submitLabel="Create Account"
          onSubmit={handleRegister}
          isLoading={isLoading}
        />

        {/* Terms and Privacy */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            By creating an account, you agree to our{' '}
            <Link href="#" className="text-primary hover:text-primary/80 transition-colors">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="#" className="text-primary hover:text-primary/80 transition-colors">
              Privacy Policy
            </Link>
          </p>
        </div>

        {/* Sign In Link */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-border" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Already have an account?
            </span>
          </div>
        </div>

        <Link href="/auth/login">
          <Button
            variant="outline"
            className="w-full hover:scale-105 transition-transform"
          >
            Sign In
          </Button>
        </Link>
      </div>
    </AuthLayout>
  )
}
